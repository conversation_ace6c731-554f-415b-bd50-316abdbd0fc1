<template>
	<view
		class="canvas-container"
		:class="{
			dragging: isDragging,
			pulse: showPulseAnimation,
			success: showSuccessAnimation,
			error: showErrorAnimation
		}"
		id="canvasContainer"
		@touchstart="onContainerTouchStart"
		@touchmove="onContainerTouchMove"
		@touchend="onContainerTouchEnd">

		<!-- 拖拽状态指示器 -->
		<view class="drag-indicator" :class="{ show: isDragging && hasDragStarted }">
			<text>拖拽中...</text>
		</view>

		<!-- Canvas画布 -->
		<canvas
			canvas-id="editCanvas"
			id="editCanvas"
			class="edit-canvas"
			:class="{ active: selectedElement }"
			:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
		></canvas>

		<!-- 选中状态指示器 -->
		<view class="selection-indicator" :class="{ show: selectedElement && !isDragging }">
			<text v-if="selectedElement">
				{{ selectedElement.type === 'text' ? '文本' : '图片' }}
				({{ Math.round(selectedElement.x) }}, {{ Math.round(selectedElement.y) }})
			</text>
		</view>
	</view>
</template>

<script>
/**
 * Canvas编辑器组件
 * 负责Canvas绘制、元素操作、触摸交互等功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-06-24
 */
export default {
	name: 'CanvasEditor',
	props: {
		/** Canvas宽度 */
		canvasWidth: {
			type: Number,
			default: 300,
			validator: (value) => value > 0
		},
		/** Canvas高度 */
		canvasHeight: {
			type: Number,
			default: 180,
			validator: (value) => value > 0
		},
		/** Canvas上的元素数组 */
		canvasElements: {
			type: Array,
			default: () => [],
			validator: (value) => Array.isArray(value)
		},
		/** 当前选中的元素 */
		selectedElement: {
			type: Object,
			default: null
		},
		/** 背景图片路径 */
		backgroundImage: {
			type: String,
			default: '',
			validator: (value) => typeof value === 'string'
		},
		/** 背景颜色 */
		backgroundColor: {
			type: String,
			default: '#FFFFFF',
			validator: (value) => /^#[0-9A-Fa-f]{6}$/.test(value)
		}
	},
	emits: [
		'element-selected',
		'element-updated',
		'element-deleted',
		'element-edit',
		'element-long-press',
		'element-copy',
		'drag-start',
		'drag-move',
		'drag-end',
		'error'
	],
	data() {
		return {
			// Canvas相关
			/** @type {CanvasContext|null} Canvas绘制上下文 */
			canvasContext: null,
			/** @type {Object|null} Canvas容器的位置信息 */
			canvasRect: null,

			// 触摸交互相关
			/** @type {number} 触摸开始时的X坐标 */
			touchStartX: 0,
			/** @type {number} 触摸开始时的Y坐标 */
			touchStartY: 0,
			/** @type {boolean} 是否正在拖拽 */
			isDragging: false,
			/** @type {boolean} 是否正在缩放 */
			isScaling: false,
			/** @type {Array|null} 上次触摸点数组 */
			lastTouches: null,
			/** @type {number} 初始两指距离 */
			initialDistance: 0,
			/** @type {number} 上次缩放因子 */
			lastScaleFactor: 1,

			// 拖拽增强功能
			/** @type {Object|null} 拖拽开始时元素的初始位置 */
			dragStartPosition: null,
			/** @type {boolean} 是否显示拖拽预览 */
			showDragPreview: false,
			/** @type {Object|null} 拖拽预览位置 */
			dragPreviewPosition: null,
			/** @type {number} 拖拽阈值，超过此距离才开始拖拽 */
			dragThreshold: 5,
			/** @type {boolean} 是否已经开始拖拽移动 */
			hasDragStarted: false,
			/** @type {number} 触摸开始时间戳 */
			touchStartTime: 0,
			/** @type {boolean} 是否启用触觉反馈 */
			enableHapticFeedback: true,

			// 操作图标位置
			/** @type {Object|null} 编辑图标位置信息 */
			editIconPosition: null,
			/** @type {Object|null} 删除图标位置信息 */
			deleteIconPosition: null,
			/** @type {Object|null} 旋转图标位置信息 */
			rotateIconPosition: null,

			// 背景图片信息缓存
			/** @type {Object|null} 背景图片信息 */
			backgroundImageInfo: null,

			// 性能优化相关
			/** @type {number|null} 绘制防抖定时器 */
			drawTimer: null,
			/** @type {number} 上次绘制时间戳 */
			lastDrawTime: 0,
			/** @type {Map} 文本测量结果缓存 */
			textMetricsCache: new Map(),
			/** @type {boolean} 是否正在绘制中 */
			isDrawing: false,

			// 边界检查和吸附
			/** @type {number} 边界吸附距离 */
			snapDistance: 10,
			/** @type {boolean} 是否启用边界吸附 */
			enableSnapping: true,
			/** @type {Array} 吸附线位置 */
			snapLines: [],

			// 触摸精度增强
			/** @type {number} 触摸目标最小尺寸 */
			minTouchTargetSize: 44,
			/** @type {Object|null} 长按定时器 */
			longPressTimer: null,
			/** @type {number} 长按触发时间 */
			longPressDelay: 500,
			/** @type {boolean} 是否正在长按 */
			isLongPressing: false,
			/** @type {Array} 触摸历史记录 */
			touchHistory: [],
			/** @type {number} 触摸历史最大长度 */
			maxTouchHistory: 10,

			// 多点触控增强
			/** @type {Object|null} 多点触控状态 */
			multiTouchState: null,
			/** @type {number} 旋转手势的初始角度 */
			initialRotationAngle: 0,
			/** @type {boolean} 是否支持旋转手势 */
			enableRotationGesture: true,

			// 视觉反馈动画
			/** @type {boolean} 显示脉冲动画 */
			showPulseAnimation: false,
			/** @type {boolean} 显示成功动画 */
			showSuccessAnimation: false,
			/** @type {boolean} 显示错误动画 */
			showErrorAnimation: false,
			/** @type {number|null} 动画定时器 */
			animationTimer: null
		}
	},
	mounted() {
		this.initCanvas();
	},
	beforeDestroy() {
		this.cleanup();
	},
	watch: {
		canvasElements: {
			handler() {
				this.debouncedDraw();
			},
			deep: true
		},
		selectedElement() {
			this.debouncedDraw();
		},
		backgroundImage() {
			this.backgroundImageInfo = null; // 清除缓存
			this.debouncedDraw();
		},
		backgroundColor() {
			this.debouncedDraw();
		}
	},
	methods: {
		/**
		 * 初始化Canvas
		 * 创建Canvas上下文并开始首次绘制
		 * @returns {void}
		 */
		initCanvas() {
			try {
				// 增加重试机制
				let retryCount = 0;
				const maxRetries = 3;
				
				const tryInit = () => {
					setTimeout(() => {
						this.canvasContext = uni.createCanvasContext('editCanvas', this);
						if (!this.canvasContext) {
							console.error('Canvas上下文创建失败, 重试次数:', retryCount);
							if (retryCount < maxRetries) {
								retryCount++;
								tryInit();
								return;
							}
							this.$emit('error', { type: 'CANVAS_INIT_ERROR', error: 'Canvas上下文创建失败' });
							return;
						}
						
						console.log('Canvas上下文创建成功');
						this.updateCanvasRect();
						
						// 延迟绘制，确保DOM完全渲染
						setTimeout(() => {
							this.debouncedDraw();
						}, 100);
					}, 200 + retryCount * 100);
				};
				
				tryInit();
			} catch (error) {
				console.error('初始化Canvas失败:', error);
				this.$emit('error', { type: 'CANVAS_INIT_ERROR', error });
			}
		},

		/**
		 * 获取Canvas容器的位置信息
		 * 用于触摸事件坐标转换
		 * @returns {void}
		 */
		updateCanvasRect() {
			try {
				let retryCount = 0;
				const maxRetries = 5;
				
				const tryGetRect = () => {
					const query = uni.createSelectorQuery().in(this);
					query.select('#canvasContainer').boundingClientRect(data => {
						if (data && data.width > 0 && data.height > 0) {
							this.canvasRect = data;
							console.log('Canvas容器位置获取成功:', data);
						} else {
							console.warn('Canvas容器位置信息无效, 重试次数:', retryCount);
							if (retryCount < maxRetries) {
								retryCount++;
								setTimeout(tryGetRect, 100 * retryCount);
							} else {
								console.error('无法获取Canvas容器位置信息，已达到最大重试次数');
								// 设置默认值
								this.canvasRect = {
									left: 0,
									top: 0,
									width: this.canvasWidth,
									height: this.canvasHeight
								};
							}
						}
					}).exec();
				};
				
				tryGetRect();
			} catch (error) {
				console.error('获取Canvas容器位置失败:', error);
				// 设置默认值
				this.canvasRect = {
					left: 0,
					top: 0,
					width: this.canvasWidth,
					height: this.canvasHeight
				};
			}
		},

		/**
		 * 防抖绘制
		 * 避免频繁重绘，提高性能
		 * @returns {void}
		 */
		debouncedDraw() {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
			}

			this.drawTimer = setTimeout(() => {
				this.drawCanvas();
			}, 16); // 约60fps
		},

		/**
		 * 可配置延迟的防抖绘制
		 * @param {number} delay - 延迟时间（毫秒）
		 * @returns {void}
		 */
		debouncedDrawWithDelay(delay = 16) {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
			}

			this.drawTimer = setTimeout(() => {
				this.drawCanvas();
			}, delay);
		},

		/**
		 * 绘制Canvas
		 * 主要绘制方法，包含性能优化和错误处理
		 * @returns {void}
		 */
		drawCanvas() {
			// 基础检查
			if (!this.canvasContext || this.isDrawing) {
				return;
			}

			// 防止重复绘制，限制最高60fps
			const now = Date.now();
			if (now - this.lastDrawTime < 16) {
				return;
			}

			try {
				this.isDrawing = true;
				this.lastDrawTime = now;

				const ctx = this.canvasContext;

				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制背景
				this.drawBackground(ctx);

				// 绘制所有元素
				this.canvasElements.forEach((element, index) => {
					try {
						this.drawElement(element);
					} catch (error) {
						console.error(`绘制元素${index}失败:`, error, element);
					}
				});

				// 绘制吸附线
				if (this.snapLines.length > 0) {
					try {
						this.drawSnapLines(ctx);
					} catch (error) {
						console.error('绘制吸附线失败:', error);
					}
				}

				// 绘制选中状态
				if (this.selectedElement) {
					try {
						this.drawSelectionHandles(this.selectedElement);
					} catch (error) {
						console.error('绘制选中状态失败:', error);
					}
				}

				// 绘制拖拽预览
				if (this.showDragPreview && this.dragPreviewPosition) {
					try {
						this.drawDragPreview(ctx);
					} catch (error) {
						console.error('绘制拖拽预览失败:', error);
					}
				}

				// 提交绘制
				ctx.draw(false, () => {
					this.isDrawing = false;
				});

			} catch (error) {
				console.error('Canvas绘制失败:', error);
				this.isDrawing = false;
				this.$emit('error', { type: 'CANVAS_DRAW_ERROR', error });
			}
		},
		
		/**
		 * 绘制背景
		 * 支持背景图片和纯色背景
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawBackground(ctx) {
			try {
				if (this.backgroundImage) {
					if (this.backgroundImageInfo) {
						// 使用缓存的图片信息绘制
						this.drawBackgroundWithRatio(ctx, this.backgroundImage, this.backgroundImageInfo);
					} else {
						// 异步获取图片信息
						uni.getImageInfo({
							src: this.backgroundImage,
							success: (imageInfo) => {
								if (imageInfo && imageInfo.width && imageInfo.height) {
									this.backgroundImageInfo = imageInfo;
									this.drawBackgroundWithRatio(ctx, this.backgroundImage, imageInfo);
									this.debouncedDraw();
								} else {
									console.warn('获取到的图片信息不完整:', imageInfo);
									this.drawSolidBackground(ctx);
								}
							},
							fail: (error) => {
								console.error('获取背景图片信息失败:', error);
								this.drawSolidBackground(ctx);
							}
						});
						// 先绘制纯色背景作为占位
						this.drawSolidBackground(ctx);
						return;
					}
				} else {
					// 绘制纯色背景
					this.drawSolidBackground(ctx);
				}
			} catch (error) {
				console.error('绘制背景失败:', error);
				// 降级到纯色背景
				this.drawSolidBackground(ctx);
			}
		},

		/**
		 * 绘制纯色背景
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawSolidBackground(ctx) {
			try {
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
			} catch (error) {
				console.error('绘制纯色背景失败:', error);
			}
		},
		
		/**
		 * 按比例绘制背景图片
		 * 保持图片宽高比，居中显示
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {string} imagePath - 图片路径
		 * @param {Object} imageInfo - 图片信息对象
		 * @returns {void}
		 */
		drawBackgroundWithRatio(ctx, imagePath, imageInfo) {
			try {
				// 参数验证
				if (!imageInfo || !imageInfo.width || !imageInfo.height) {
					console.error('图片信息不完整:', imageInfo);
					this.drawSolidBackground(ctx);
					return;
				}

				const imageRatio = imageInfo.width / imageInfo.height;
				const canvasRatio = this.canvasWidth / this.canvasHeight;

				let drawWidth, drawHeight, offsetX = 0, offsetY = 0;

				// 计算绘制尺寸和偏移量
				if (imageRatio > canvasRatio) {
					// 图片更宽，以高度为基准
					drawHeight = this.canvasHeight;
					drawWidth = drawHeight * imageRatio;
					offsetX = (this.canvasWidth - drawWidth) / 2;
				} else {
					// 图片更高，以宽度为基准
					drawWidth = this.canvasWidth;
					drawHeight = drawWidth / imageRatio;
					offsetY = (this.canvasHeight - drawHeight) / 2;
				}

				// 先填充背景色
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制图片
				ctx.drawImage(imagePath, offsetX, offsetY, drawWidth, drawHeight);

			} catch (error) {
				console.error('按比例绘制背景图片失败:', error);
				// 降级到纯色背景
				this.drawSolidBackground(ctx);
			}
		},
		
		/**
		 * 绘制单个元素
		 * 根据元素类型调用相应的绘制方法
		 * @param {Object} element - 要绘制的元素对象
		 * @returns {void}
		 */
		drawElement(element) {
			// 参数验证
			if (!element || !element.type) {
				console.warn('元素对象无效:', element);
				return;
			}

			const ctx = this.canvasContext;
			if (!ctx) {
				console.error('Canvas上下文不存在');
				return;
			}

			ctx.save();

			try {
				switch (element.type) {
					case 'text':
						this.drawTextElement(ctx, element);
						break;
					case 'image':
						this.drawImageElement(ctx, element);
						break;
					default:
						console.warn('未知的元素类型:', element.type);
				}
			} catch (error) {
				console.error(`绘制${element.type}元素失败:`, error, element);
			} finally {
				ctx.restore();
			}
		},
		
		/**
		 * 绘制文本元素
		 * 支持字体样式、颜色、对齐方式、高度拉伸等
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 文本元素对象
		 * @returns {void}
		 */
		drawTextElement(ctx, element) {
			try {
				// 参数验证
				if (!element.text || typeof element.text !== 'string') {
					console.warn('文本内容无效:', element.text);
					return;
				}

				// 设置文本颜色
				const color = element.color || '#000000';
				if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
					console.warn('文本颜色格式无效:', color);
				}
				ctx.setFillStyle(color);

				// 设置字体样式
				const fontStyle = element.isItalic ? 'italic' : 'normal';
				const fontWeight = element.isBold ? 'bold' : 'normal';
				const fontSize = Math.max(8, Math.min(80, element.fontSize || 16)); // 限制字号范围
				const fontFamily = element.fontFamily || 'sans-serif';

				try {
					ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
				} catch (error) {
					console.warn('设置字体失败，使用默认字体:', error);
					ctx.font = `${fontSize}px sans-serif`;
				}

				// 设置文本对齐
				const textAlign = ['left', 'center', 'right'].includes(element.textAlign)
					? element.textAlign : 'left';
				ctx.setTextAlign(textAlign);

				// 处理高度拉伸
				const heightScale = element.heightScale || 1;
				if (heightScale !== 1 && heightScale > 0) {
					ctx.scale(1, heightScale);
					const scaledY = element.y / heightScale;
					ctx.fillText(element.text, element.x, scaledY);
				} else {
					ctx.fillText(element.text, element.x, element.y);
				}

				// 绘制文本装饰线
				if (element.isUnderline || element.isStrikethrough) {
					this.drawTextDecorations(ctx, element);
				}

			} catch (error) {
				console.error('绘制文本元素失败:', error, element);
			}
		},
		
		/**
		 * 绘制图片元素
		 * 支持位置约束、旋转等功能
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 图片元素对象
		 * @returns {void}
		 */
		drawImageElement(ctx, element) {
			try {
				// 参数验证
				if (!element.src) {
					console.warn('图片源路径为空:', element);
					return;
				}

				if (!element.width || !element.height || element.width <= 0 || element.height <= 0) {
					console.warn('图片尺寸无效:', element.width, element.height);
					return;
				}

				// 边界约束
				let x = Math.max(0, Math.min(element.x || 0, this.canvasWidth - element.width));
				let y = Math.max(0, Math.min(element.y || 0, this.canvasHeight - element.height));

				// 更新元素位置（如果发生了约束）
				if (x !== element.x || y !== element.y) {
					element.x = x;
					element.y = y;
				}

				// 处理旋转
				const rotation = element.rotation || 0;
				if (rotation !== 0) {
					const centerX = x + element.width / 2;
					const centerY = y + element.height / 2;

					// 验证旋转角度
					const normalizedRotation = rotation % 360;

					ctx.translate(centerX, centerY);
					ctx.rotate(normalizedRotation * Math.PI / 180);
					ctx.drawImage(element.src, -element.width / 2, -element.height / 2, element.width, element.height);
				} else {
					// 无旋转时直接绘制
					ctx.drawImage(element.src, x, y, element.width, element.height);
				}

			} catch (error) {
				console.error('绘制图片元素失败:', error, element);
				// 可以在这里绘制一个错误占位符
				this.drawErrorPlaceholder(ctx, element);
			}
		},

		/**
		 * 绘制错误占位符
		 * 当图片加载失败时显示
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} element - 元素对象
		 * @returns {void}
		 */
		drawErrorPlaceholder(ctx, element) {
			try {
				const x = element.x || 0;
				const y = element.y || 0;
				const width = element.width || 50;
				const height = element.height || 50;

				// 绘制错误占位框
				ctx.setStrokeStyle('#ff0000');
				ctx.setLineWidth(2);
				ctx.strokeRect(x, y, width, height);

				// 绘制X标记
				ctx.beginPath();
				ctx.moveTo(x, y);
				ctx.lineTo(x + width, y + height);
				ctx.moveTo(x + width, y);
				ctx.lineTo(x, y + height);
				ctx.stroke();

			} catch (error) {
				console.error('绘制错误占位符失败:', error);
			}
		},
		
		// 绘制文本装饰线
		drawTextDecorations(ctx, element) {
			const metrics = ctx.measureText(element.text);
			const textWidth = metrics.width;
			let lineY = element.y;
			
			if (element.heightScale && element.heightScale !== 1) {
				lineY = element.y / element.heightScale;
			}
			
			if (element.isUnderline) {
				lineY = lineY + 2;
			}
			if (element.isStrikethrough) {
				lineY = lineY - (element.fontSize || 16) / 3;
			}
			
			let lineStartX = element.x;
			if (element.textAlign === 'center') {
				lineStartX = element.x - textWidth / 2;
			} else if (element.textAlign === 'right') {
				lineStartX = element.x - textWidth;
			}
			
			ctx.beginPath();
			ctx.setStrokeStyle(element.color || '#000000');
			ctx.setLineWidth(1);
			ctx.moveTo(lineStartX, lineY);
			ctx.lineTo(lineStartX + textWidth, lineY);
			ctx.stroke();
		},
		
		// 绘制选中状态
		drawSelectionHandles(element) {
			const ctx = this.canvasContext;
			const box = this.getElementBoundingBox(element);

			ctx.save();

			// 绘制选中边框
			ctx.setStrokeStyle(this.hasDragStarted ? '#ff6b35' : '#0969da');
			ctx.setLineWidth(this.hasDragStarted ? 3 : 2);

			if (element.type === 'image' && element.rotation && element.rotation !== 0) {
				const centerX = element.x + element.width / 2;
				const centerY = element.y + element.height / 2;

				ctx.translate(centerX, centerY);
				ctx.rotate(element.rotation * Math.PI / 180);

				const halfWidth = element.width / 2;
				const halfHeight = element.height / 2;
				ctx.strokeRect(-halfWidth, -halfHeight, element.width, element.height);

				ctx.rotate(-element.rotation * Math.PI / 180);
				ctx.translate(-centerX, -centerY);
			} else {
				// 绘制虚线边框（拖拽时）
				if (this.hasDragStarted) {
					ctx.setLineDash([8, 4]);
				}
				ctx.strokeRect(box.x, box.y, box.width, box.height);
				ctx.setLineDash([]);
			}

			// 绘制选中角点
			if (!this.hasDragStarted) {
				this.drawSelectionCorners(ctx, box);
			}

			// 绘制操作图标（非拖拽状态时）
			if (!this.hasDragStarted) {
				this.drawOperationIcons(ctx, element, box);
			}

			ctx.restore();
		},

		/**
		 * 绘制选中状态的角点
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @param {Object} box - 元素边界框
		 * @returns {void}
		 */
		drawSelectionCorners(ctx, box) {
			const cornerSize = 8;
			const corners = [
				{ x: box.x, y: box.y }, // 左上
				{ x: box.x + box.width, y: box.y }, // 右上
				{ x: box.x, y: box.y + box.height }, // 左下
				{ x: box.x + box.width, y: box.y + box.height } // 右下
			];

			ctx.setFillStyle('#0969da');
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(1);

			corners.forEach(corner => {
				ctx.fillRect(
					corner.x - cornerSize / 2,
					corner.y - cornerSize / 2,
					cornerSize,
					cornerSize
				);
				ctx.strokeRect(
					corner.x - cornerSize / 2,
					corner.y - cornerSize / 2,
					cornerSize,
					cornerSize
				);
			});
		},

		/**
		 * 绘制吸附线
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawSnapLines(ctx) {
			if (!this.snapLines || this.snapLines.length === 0) return;

			ctx.save();
			ctx.setStrokeStyle('#ff6b35');
			ctx.setLineWidth(1);
			ctx.setLineDash([4, 4]);

			this.snapLines.forEach(line => {
				ctx.beginPath();
				if (line.type === 'vertical') {
					ctx.moveTo(line.x, line.y1);
					ctx.lineTo(line.x, line.y2);
				} else if (line.type === 'horizontal') {
					ctx.moveTo(line.x1, line.y);
					ctx.lineTo(line.x2, line.y);
				}
				ctx.stroke();
			});

			ctx.setLineDash([]);
			ctx.restore();
		},

		/**
		 * 绘制拖拽预览
		 * @param {CanvasContext} ctx - Canvas绘制上下文
		 * @returns {void}
		 */
		drawDragPreview(ctx) {
			if (!this.selectedElement || !this.dragPreviewPosition) return;

			const box = this.getElementBoundingBox(this.selectedElement);

			ctx.save();
			ctx.setStrokeStyle('#ff6b35');
			ctx.setLineWidth(2);
			ctx.setLineDash([6, 6]);
			ctx.globalAlpha = 0.6;

			// 绘制预览边框
			ctx.strokeRect(
				this.dragPreviewPosition.x,
				this.dragPreviewPosition.y,
				box.width,
				box.height
			);

			ctx.setLineDash([]);
			ctx.restore();
		},
		
		// 绘制操作图标
		drawOperationIcons(ctx, element, box) {
			const iconRadius = 18;
			
			if (element.type === 'image') {
				// 图片元素显示删除和旋转图标
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				const rotateIconX = box.x - iconRadius;
				const rotateIconY = box.y - iconRadius;
				
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = { x: rotateIconX, y: rotateIconY, radius: iconRadius * 1.5 };
				this.editIconPosition = null;
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制旋转图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(rotateIconX, rotateIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
				this.drawRotateIcon(ctx, rotateIconX, rotateIconY, iconRadius);
				
			} else {
				// 文本元素显示编辑和删除图标
				const editIconX = box.x - iconRadius;
				const editIconY = box.y - iconRadius;
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				
				this.editIconPosition = { x: editIconX, y: editIconY, radius: iconRadius * 1.5 };
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = null;
				
				// 绘制编辑图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(editIconX, editIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawEditIcon(ctx, editIconX, editIconY);
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
			}
		},

		// 绘制删除图标
		drawDeleteIcon(ctx, x, y) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2.5);
			ctx.moveTo(x - 7, y - 7);
			ctx.lineTo(x + 7, y + 7);
			ctx.moveTo(x + 7, y - 7);
			ctx.lineTo(x - 7, y + 7);
			ctx.stroke();
		},

		// 绘制编辑图标
		drawEditIcon(ctx, x, y) {
			ctx.setFillStyle('#ffffff');
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.beginPath();
			ctx.moveTo(x - 7, y + 7);
			ctx.lineTo(x + 5, y - 5);
			ctx.lineTo(x + 7, y - 3);
			ctx.lineTo(x - 5, y + 9);
			ctx.closePath();
			ctx.fill();
		},

		// 绘制旋转图标
		drawRotateIcon(ctx, x, y, radius) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.arc(x, y, radius * 0.6, 0.3 * Math.PI, 2.2 * Math.PI);
			ctx.stroke();

			ctx.beginPath();
			ctx.moveTo(x + 6, y - 6);
			ctx.lineTo(x, y - 10);
			ctx.lineTo(x - 4, y - 6);
			ctx.setFillStyle('#ffffff');
			ctx.fill();
		},

		// 获取文本宽度（带缓存）
		getTextWidth(element) {
			const cacheKey = `${element.text}_${element.fontSize}_${element.fontFamily}_${element.isBold}_${element.isItalic}`;

			if (this.textMetricsCache.has(cacheKey)) {
				return this.textMetricsCache.get(cacheKey);
			}

			const ctx = this.canvasContext;
			const fontStyle = element.isItalic ? 'italic' : 'normal';
			const fontWeight = element.isBold ? 'bold' : 'normal';
			const fontSize = element.fontSize || 16;
			const fontFamily = element.fontFamily || 'sans-serif';

			ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;

			let width = 0;
			try {
				const metrics = ctx.measureText(element.text);
				width = metrics.width;
			} catch (e) {
				width = element.text.length * fontSize * 0.6;
			}

			// 缓存结果，限制缓存大小
			if (this.textMetricsCache.size > 100) {
				const firstKey = this.textMetricsCache.keys().next().value;
				this.textMetricsCache.delete(firstKey);
			}
			this.textMetricsCache.set(cacheKey, width);

			return width;
		},

		// 获取元素边界框
		getElementBoundingBox(element) {
			if (element.type === 'image') {
				return {
					x: element.x,
					y: element.y,
					width: element.width,
					height: element.height
				};
			} else if (element.type === 'text') {
				const fontSize = element.fontSize || 16;
				const width = this.getTextWidth(element);

				let x = element.x;
				if (element.textAlign === 'center') {
					x = element.x - width / 2;
				} else if (element.textAlign === 'right') {
					x = element.x - width;
				}

				return {
					x: x,
					y: element.y - fontSize,
					width: width,
					height: fontSize * 1.5
				};
			}

			return {
				x: element.x,
				y: element.y,
				width: 10,
				height: 10
			};
		},

		// 触摸事件处理
		onContainerTouchStart(e) {

			e.preventDefault && e.preventDefault();

			// 记录触摸开始时间
			this.touchStartTime = Date.now();

			// 记录触摸历史
			this.addTouchToHistory(e.touches[0]);

			// 处理多点触控
			if (e.touches.length === 2) {
				this.handleMultiTouchStart(e);
				return;
			}

			// 清除长按定时器
			this.clearLongPressTimer();

			const touch = e.touches[0];

			// 确保Canvas矩形信息可用
			if (!this.canvasRect || this.canvasRect.width === 0) {

				this.updateCanvasRect();
				// 延迟处理触摸事件
				setTimeout(() => {
					if (this.canvasRect && this.canvasRect.width > 0) {
						this.processTouchStart(touch);
					}
				}, 200);
				return;
			}

			this.processTouchStart(touch);
		},
		
		/**
		 * 处理触摸开始的核心逻辑
		 * @param {Touch} touch - 触摸对象
		 * @returns {void}
		 */
		processTouchStart(touch) {
			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			this.touchStartX = canvasX;
			this.touchStartY = canvasY;

			// 检查是否点击了操作图标
			if (this.selectedElement) {
				const iconHit = this.checkIconHit(canvasX, canvasY);
				if (iconHit) {
					this.handleIconClick(iconHit);
					return;
				}
			}

			// 选择元素
			const selectedElement = this.selectElementAt(canvasX, canvasY);
			
			// 通知父组件更新选中元素
			this.$emit('element-selected', selectedElement);

			if (selectedElement) {
				// 准备拖拽
				this.isDragging = true;
				this.hasDragStarted = false;
				this.showDragPreview = false;

				// 记录拖拽开始位置（元素的当前位置）
				this.dragStartPosition = {
					x: selectedElement.x,
					y: selectedElement.y
				};
			} else {
				// 点击空白区域，清除选择
				this.resetDragState();
			}
		},

		onContainerTouchMove(e) {
			// 记录触摸历史
			this.addTouchToHistory(e.touches[0]);

			// 清除长按检测（移动时取消长按）
			this.clearLongPressTimer();

			// 处理多点触控
			if (e.touches.length === 2) {
				this.handleMultiTouchMove(e);
				return;
			}

			// 处理单点拖拽
			this.handleSingleTouchMove(e);
		},

		/**
		 * 处理单点触摸移动
		 * @param {TouchEvent} e - 触摸事件
		 * @returns {void}
		 */
		handleSingleTouchMove(e) {
			// 检查是否可以开始拖拽
			if (!this.isDragging || !this.selectedElement || !this.canvasRect) {
				return;
			}

			e.preventDefault && e.preventDefault();

			const touch = e.touches[0];
			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			// 使用固定的触摸起始点计算增量，避免重复更新导致的计算错误
			const deltaX = canvasX - this.touchStartX;
			const deltaY = canvasY - this.touchStartY;
			const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

			// 检查是否超过拖拽阈值
			if (!this.hasDragStarted && distance < this.dragThreshold) {
				return; // 还未达到拖拽阈值，不开始拖拽
			}

			// 首次开始拖拽
			if (!this.hasDragStarted) {
				this.hasDragStarted = true;
				this.showDragPreview = true;

				// 发送拖拽开始事件
				this.$emit('drag-start', {
					element: this.selectedElement,
					startPosition: this.dragStartPosition
				});
			}

			// 基于拖拽起始位置计算新位置
			let newX = this.dragStartPosition.x + deltaX;
			let newY = this.dragStartPosition.y + deltaY;

			// 使用约束方法进行边界检查
			const constrainedPosition = this.constrainElementPosition(newX, newY, this.selectedElement);
			newX = constrainedPosition.x;
			newY = constrainedPosition.y;

			// 更新元素位置
			this.selectedElement.x = newX;
			this.selectedElement.y = newY;

			// 不更新触摸起始点，保持基于原始拖拽起始位置计算
			// this.touchStartX = canvasX;
			// this.touchStartY = canvasY;

			// 发送更新事件
			this.$emit('element-updated', this.selectedElement);

			// 使用更长的防抖延迟来减少拖拽时的重绘频率
			this.debouncedDrawWithDelay(8); // 约120fps，提升拖拽流畅度
		},

		onContainerTouchEnd() {

			const touchDuration = Date.now() - this.touchStartTime;

			// 如果是短时间点击且没有拖拽，可能是选择操作
			if (touchDuration < 200 && !this.hasDragStarted && this.selectedElement) {
				// 短点击选中元素，给予触觉反馈
				this.triggerHapticFeedback('light');
			}

			// 如果进行了拖拽操作
			if (this.hasDragStarted && this.selectedElement) {
				// 发送拖拽结束事件
				this.$emit('drag-end', {
					element: this.selectedElement,
					startPosition: this.dragStartPosition,
					endPosition: { x: this.selectedElement.x, y: this.selectedElement.y },
					duration: touchDuration
				});

				// 检查是否移动了足够的距离
				const moved = this.dragStartPosition && (
					Math.abs(this.selectedElement.x - this.dragStartPosition.x) > 5 ||
					Math.abs(this.selectedElement.y - this.dragStartPosition.y) > 5
				);

				if (moved) {
					this.triggerHapticFeedback('medium');
					this.triggerAnimation('success', 400);
				}
			}

			// 重置拖拽状态
			this.resetDragState();
			
			// 清除长按定时器
			this.clearLongPressTimer();
			
			// 触发重绘以更新界面
			this.debouncedDraw();
		},

		// 判断点是否在圆内
		isPointInCircle(x, y, circle) {
			const dx = x - circle.x;
			const dy = y - circle.y;
			const distance = Math.sqrt(dx * dx + dy * dy);
			return distance <= circle.radius;
		},

		// 旋转选中的元素
		rotateSelectedElement() {
			if (!this.selectedElement || this.selectedElement.type !== 'image') return;

			if (this.selectedElement.rotation === undefined) {
				this.selectedElement.rotation = 0;
			}

			this.selectedElement.rotation = (this.selectedElement.rotation + 45) % 360;

			this.$emit('element-updated', this.selectedElement);
			this.debouncedDraw();

			uni.showToast({
				title: `已旋转至${this.selectedElement.rotation}°`,
				icon: 'none',
				duration: 1500
			});
		},

		// 选择元素
		selectElementAt(x, y) {
			for (let i = this.canvasElements.length - 1; i >= 0; i--) {
				const element = this.canvasElements[i];
				const box = this.getElementBoundingBox(element);

				if (x >= box.x && x <= box.x + box.width && y >= box.y && y <= box.y + box.height) {
					return element;
				}
			}
			return null;
		},

		/**
		 * 重置拖拽状态
		 * @returns {void}
		 */
		resetDragState() {
			this.isDragging = false;
			this.isScaling = false;
			this.hasDragStarted = false;
			this.showDragPreview = false;
			this.lastScaleFactor = 1;
			this.lastTouches = null;
			this.dragStartPosition = null;
			this.dragPreviewPosition = null;
			this.snapLines = [];
			this.touchStartX = 0;
			this.touchStartY = 0;
			this.touchStartTime = 0;
			// 注意：不在这里清除selectedElement，因为选中状态应该保持
			// this.selectedElement = null;
			this.clearLongPressTimer();
		},

		/**
		 * 触发触觉反馈
		 * @param {string} type - 反馈类型: 'light', 'medium', 'heavy'
		 * @returns {void}
		 */
		triggerHapticFeedback(type = 'light') {
			if (!this.enableHapticFeedback) return;

			try {
				// #ifdef APP-PLUS
				if (uni.vibrateShort) {
					uni.vibrateShort({
						type: type === 'heavy' ? 'heavy' : type === 'medium' ? 'medium' : 'light'
					});
				}
				// #endif

				// #ifdef H5
				if (navigator.vibrate) {
					const duration = type === 'heavy' ? 50 : type === 'medium' ? 30 : 15;
					navigator.vibrate(duration);
				}
				// #endif
			} catch (error) {
				// 忽略触觉反馈错误
				console.debug('触觉反馈不可用:', error);
			}
		},

		/**
		 * 约束元素位置在画布边界内
		 * @param {number} x - 目标X坐标
		 * @param {number} y - 目标Y坐标
		 * @param {Object} element - 元素对象
		 * @returns {Object} 约束后的位置 {x, y}
		 */
		constrainElementPosition(x, y, element) {
			const box = this.getElementBoundingBox(element);
			const elementWidth = box.width;
			const elementHeight = box.height;

			let constrainedX = x;
			let constrainedY = y;

			// X轴边界检查
			if (element.type === 'text') {
				// 文本元素根据对齐方式调整边界
				if (element.textAlign === 'center') {
					constrainedX = Math.max(elementWidth / 2, Math.min(x, this.canvasWidth - elementWidth / 2));
				} else if (element.textAlign === 'right') {
					constrainedX = Math.max(elementWidth, Math.min(x, this.canvasWidth));
				} else {
					constrainedX = Math.max(0, Math.min(x, this.canvasWidth - elementWidth));
				}
			} else {
				constrainedX = Math.max(0, Math.min(x, this.canvasWidth - elementWidth));
			}

			// Y轴边界检查
			if (element.type === 'text') {
				// 文本元素的Y坐标是基线位置
				constrainedY = Math.max(elementHeight, Math.min(y, this.canvasHeight));
			} else {
				constrainedY = Math.max(0, Math.min(y, this.canvasHeight - elementHeight));
			}

			return { x: constrainedX, y: constrainedY };
		},

		/**
		 * 计算吸附位置
		 * @param {number} x - 目标X坐标
		 * @param {number} y - 目标Y坐标
		 * @returns {Object} 吸附后的位置和吸附线信息
		 */
		calculateSnappedPosition(x, y) {
			if (!this.enableSnapping || !this.selectedElement) {
				return { x, y, snapLines: [] };
			}

			const snapLines = [];
			let snappedX = x;
			let snappedY = y;

			// 画布边界吸附
			const canvasSnapPoints = [
				{ type: 'canvas', axis: 'x', value: 0, label: '左边界' },
				{ type: 'canvas', axis: 'x', value: this.canvasWidth / 2, label: '水平中心' },
				{ type: 'canvas', axis: 'x', value: this.canvasWidth, label: '右边界' },
				{ type: 'canvas', axis: 'y', value: 0, label: '上边界' },
				{ type: 'canvas', axis: 'y', value: this.canvasHeight / 2, label: '垂直中心' },
				{ type: 'canvas', axis: 'y', value: this.canvasHeight, label: '下边界' }
			];

			// 检查画布边界吸附
			for (const snapPoint of canvasSnapPoints) {
				if (snapPoint.axis === 'x') {
					if (Math.abs(x - snapPoint.value) <= this.snapDistance) {
						snappedX = snapPoint.value;
						snapLines.push({
							type: 'vertical',
							x: snapPoint.value,
							y1: 0,
							y2: this.canvasHeight,
							label: snapPoint.label
						});
					}
				} else if (snapPoint.axis === 'y') {
					if (Math.abs(y - snapPoint.value) <= this.snapDistance) {
						snappedY = snapPoint.value;
						snapLines.push({
							type: 'horizontal',
							x1: 0,
							x2: this.canvasWidth,
							y: snapPoint.value,
							label: snapPoint.label
						});
					}
				}
			}

			// 其他元素吸附
			for (const element of this.canvasElements) {
				if (element === this.selectedElement) continue;

				const elementBox = this.getElementBoundingBox(element);
				const elementSnapPoints = [
					{ axis: 'x', value: elementBox.x, label: '左对齐' },
					{ axis: 'x', value: elementBox.x + elementBox.width / 2, label: '中心对齐' },
					{ axis: 'x', value: elementBox.x + elementBox.width, label: '右对齐' },
					{ axis: 'y', value: elementBox.y, label: '顶部对齐' },
					{ axis: 'y', value: elementBox.y + elementBox.height / 2, label: '中心对齐' },
					{ axis: 'y', value: elementBox.y + elementBox.height, label: '底部对齐' }
				];

				for (const snapPoint of elementSnapPoints) {
					if (snapPoint.axis === 'x') {
						if (Math.abs(x - snapPoint.value) <= this.snapDistance) {
							snappedX = snapPoint.value;
							snapLines.push({
								type: 'vertical',
								x: snapPoint.value,
								y1: Math.min(y, elementBox.y) - 10,
								y2: Math.max(y + this.getElementBoundingBox(this.selectedElement).height,
									elementBox.y + elementBox.height) + 10,
								label: snapPoint.label
							});
						}
					} else if (snapPoint.axis === 'y') {
						if (Math.abs(y - snapPoint.value) <= this.snapDistance) {
							snappedY = snapPoint.value;
							snapLines.push({
								type: 'horizontal',
								x1: Math.min(x, elementBox.x) - 10,
								x2: Math.max(x + this.getElementBoundingBox(this.selectedElement).width,
									elementBox.x + elementBox.width) + 10,
								y: snapPoint.value,
								label: snapPoint.label
							});
						}
					}
				}
			}

			return { x: snappedX, y: snappedY, snapLines };
		},

		/**
		 * 添加触摸历史记录
		 * @param {Touch} touch - 触摸对象
		 * @returns {void}
		 */
		addTouchToHistory(touch) {
			const touchPoint = {
				x: touch.clientX,
				y: touch.clientY,
				timestamp: Date.now()
			};

			this.touchHistory.push(touchPoint);

			// 限制历史记录长度
			if (this.touchHistory.length > this.maxTouchHistory) {
				this.touchHistory.shift();
			}
		},

		/**
		 * 清除长按定时器
		 * @returns {void}
		 */
		clearLongPressTimer() {
			if (this.longPressTimer) {
				clearTimeout(this.longPressTimer);
				this.longPressTimer = null;
			}
			this.isLongPressing = false;
		},

		/**
		 * 启动长按检测
		 * @param {Object} element - 选中的元素
		 * @returns {void}
		 */
		startLongPressDetection(element) {
			this.clearLongPressTimer();

			this.longPressTimer = setTimeout(() => {
				if (!this.hasDragStarted && this.selectedElement === element) {
					this.isLongPressing = true;
					this.triggerHapticFeedback('medium');

					// 发送长按事件
					this.$emit('element-long-press', element);

					// 显示上下文菜单或其他长按操作
					this.showElementContextMenu(element);
				}
			}, this.longPressDelay);
		},

		/**
		 * 显示元素上下文菜单
		 * @param {Object} element - 元素对象
		 * @returns {void}
		 */
		showElementContextMenu(element) {
			const actions = [];

			if (element.type === 'text') {
				actions.push('编辑文本', '复制', '删除');
			} else if (element.type === 'image') {
				actions.push('旋转', '复制', '删除');
			}

			uni.showActionSheet({
				itemList: actions,
				success: (res) => {
					this.handleContextMenuAction(element, actions[res.tapIndex]);
				}
			});
		},

		/**
		 * 处理上下文菜单操作
		 * @param {Object} element - 元素对象
		 * @param {string} action - 操作类型
		 * @returns {void}
		 */
		handleContextMenuAction(element, action) {
			switch (action) {
				case '编辑文本':
					this.$emit('element-edit', element);
					break;
				case '旋转':
					this.rotateSelectedElement();
					break;
				case '复制':
					this.$emit('element-copy', element);
					break;
				case '删除':
					this.$emit('element-deleted', element);
					break;
			}
		},

		/**
		 * 处理多点触控开始
		 * @param {TouchEvent} e - 触摸事件
		 * @returns {void}
		 */
		handleMultiTouchStart(e) {
			if (this.selectedElement && this.selectedElement.type === 'image') {
				this.isScaling = true;
				this.isDragging = false;
				this.hasDragStarted = false;

				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				this.initialDistance = Math.sqrt(dx * dx + dy * dy);

				// 计算初始旋转角度
				if (this.enableRotationGesture) {
					this.initialRotationAngle = Math.atan2(dy, dx) * 180 / Math.PI;
				}

				this.lastTouches = [...e.touches];
				this.triggerHapticFeedback('light');
			}
		},

		/**
		 * 处理多点触控移动
		 * @param {TouchEvent} e - 触摸事件
		 * @returns {void}
		 */
		handleMultiTouchMove(e) {
			if (this.isScaling && this.selectedElement && this.selectedElement.type === 'image') {
				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				const currentDistance = Math.sqrt(dx * dx + dy * dy);

				// 处理缩放
				const scaleFactor = currentDistance / this.initialDistance;
				if (scaleFactor > 0.1) {
					this.handleImageScaling(scaleFactor);
				}

				// 处理旋转手势
				if (this.enableRotationGesture) {
					const currentAngle = Math.atan2(dy, dx) * 180 / Math.PI;
					const rotationDelta = currentAngle - this.initialRotationAngle;

					// 只有旋转角度变化超过阈值才应用
					if (Math.abs(rotationDelta) > 5) {
						this.handleImageRotation(rotationDelta);
					}
				}

				this.lastTouches = [...e.touches];
			}
		},

		/**
		 * 处理图片缩放
		 * @param {number} scaleFactor - 缩放因子
		 * @returns {void}
		 */
		handleImageScaling(scaleFactor) {
			const originalWidth = this.selectedElement.width / (this.lastScaleFactor || 1);
			const originalHeight = this.selectedElement.height / (this.lastScaleFactor || 1);

			let newWidth = originalWidth * scaleFactor;
			let newHeight = originalHeight * scaleFactor;

			// 尺寸限制
			const minSize = 30;
			const maxWidth = this.canvasWidth * 0.95;
			const maxHeight = this.canvasHeight * 0.95;

			if (newWidth < minSize) {
				newWidth = minSize;
				newHeight = (minSize / originalWidth) * originalHeight;
			}
			if (newHeight < minSize) {
				newHeight = minSize;
				newWidth = (minSize / originalHeight) * originalWidth;
			}
			if (newWidth > maxWidth) {
				newWidth = maxWidth;
				newHeight = (maxWidth / originalWidth) * originalHeight;
			}
			if (newHeight > maxHeight) {
				newHeight = maxHeight;
				newWidth = (maxHeight / originalHeight) * originalWidth;
			}

			this.selectedElement.width = newWidth;
			this.selectedElement.height = newHeight;
			this.lastScaleFactor = scaleFactor;

			// 边界约束
			const constrainedPosition = this.constrainElementPosition(
				this.selectedElement.x,
				this.selectedElement.y,
				this.selectedElement
			);
			this.selectedElement.x = constrainedPosition.x;
			this.selectedElement.y = constrainedPosition.y;

			this.$emit('element-updated', this.selectedElement);
			this.debouncedDraw();
		},

		/**
		 * 处理图片旋转手势
		 * @param {number} rotationDelta - 旋转角度变化
		 * @returns {void}
		 */
		handleImageRotation(rotationDelta) {
			if (!this.selectedElement || this.selectedElement.type !== 'image') return;

			if (this.selectedElement.rotation === undefined) {
				this.selectedElement.rotation = 0;
			}

			// 应用旋转，限制在0-360度范围内
			this.selectedElement.rotation = (this.selectedElement.rotation + rotationDelta) % 360;
			if (this.selectedElement.rotation < 0) {
				this.selectedElement.rotation += 360;
			}

			this.$emit('element-updated', this.selectedElement);
			this.debouncedDraw();
		},

		/**
		 * 检查图标点击
		 * @param {number} x - X坐标
		 * @param {number} y - Y坐标
		 * @returns {string|null} 点击的图标类型
		 */
		checkIconHit(x, y) {
			if (this.deleteIconPosition && this.isPointInCircle(x, y, this.deleteIconPosition)) {
				return 'delete';
			}
			if (this.editIconPosition && this.isPointInCircle(x, y, this.editIconPosition)) {
				return 'edit';
			}
			if (this.rotateIconPosition && this.isPointInCircle(x, y, this.rotateIconPosition)) {
				return 'rotate';
			}
			return null;
		},

		/**
		 * 处理图标点击
		 * @param {string} iconType - 图标类型
		 * @returns {void}
		 */
		handleIconClick(iconType) {
			this.triggerHapticFeedback(iconType === 'delete' ? 'medium' : 'light');

			setTimeout(() => {
				switch (iconType) {
					case 'delete':
						this.$emit('element-deleted', this.selectedElement);
						break;
					case 'edit':
						this.$emit('element-edit', this.selectedElement);
						break;
					case 'rotate':
						this.rotateSelectedElement();
						break;
				}
			}, 100);
		},

		/**
		 * 增强的元素选择
		 * 考虑触摸目标最小尺寸
		 * @param {number} x - X坐标
		 * @param {number} y - Y坐标
		 * @returns {Object|null} 选中的元素
		 */
		selectElementAtEnhanced(x, y) {
			for (let i = this.canvasElements.length - 1; i >= 0; i--) {
				const element = this.canvasElements[i];
				const box = this.getElementBoundingBox(element);

				// 扩展触摸区域以满足最小触摸目标尺寸
				const expandX = Math.max(0, (this.minTouchTargetSize - box.width) / 2);
				const expandY = Math.max(0, (this.minTouchTargetSize - box.height) / 2);

				const hitBox = {
					x: box.x - expandX,
					y: box.y - expandY,
					width: box.width + expandX * 2,
					height: box.height + expandY * 2
				};

				if (x >= hitBox.x && x <= hitBox.x + hitBox.width &&
					y >= hitBox.y && y <= hitBox.y + hitBox.height) {
					return element;
				}
			}
			return null;
		},

		/**
		 * 触发视觉反馈动画
		 * @param {string} type - 动画类型: 'pulse', 'success', 'error'
		 * @param {number} duration - 动画持续时间
		 * @returns {void}
		 */
		triggerAnimation(type, duration = 600) {
			// 清除之前的动画
			this.clearAnimation();

			// 设置动画状态
			switch (type) {
				case 'pulse':
					this.showPulseAnimation = true;
					break;
				case 'success':
					this.showSuccessAnimation = true;
					break;
				case 'error':
					this.showErrorAnimation = true;
					break;
			}

			// 设置定时器清除动画
			this.animationTimer = setTimeout(() => {
				this.clearAnimation();
			}, duration);
		},

		/**
		 * 清除所有动画状态
		 * @returns {void}
		 */
		clearAnimation() {
			if (this.animationTimer) {
				clearTimeout(this.animationTimer);
				this.animationTimer = null;
			}
			this.showPulseAnimation = false;
			this.showSuccessAnimation = false;
			this.showErrorAnimation = false;
		},



		// 清理资源
		cleanup() {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
				this.drawTimer = null;
			}
			this.clearLongPressTimer();
			this.clearAnimation();
			this.textMetricsCache.clear();
			this.canvasContext = null;
			this.canvasRect = null;
			this.resetDragState();
		},

		// 公共方法
		exportCanvas() {
			return new Promise((resolve, reject) => {
				uni.canvasToTempFilePath({
					canvasId: 'editCanvas',
					success: resolve,
					fail: reject
				}, this);
			});
		}
	}
}
</script>

<style scoped>
.canvas-container {
	padding: 10px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	flex: 0 0 33.33vh;
	position: relative;
	transition: background-color 0.3s ease;
}

.canvas-container.dragging {
	background-color: #f8f9fa;
}

.edit-canvas {
	background-color: #f5f5f5;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.edit-canvas:hover {
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.edit-canvas.active {
	border-color: #0969da;
	box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2);
}

/* 拖拽状态指示器 */
.drag-indicator {
	position: absolute;
	top: 10px;
	right: 10px;
	background-color: #0969da;
	color: white;
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
	opacity: 0;
	transform: translateY(-10px);
	transition: all 0.3s ease;
	z-index: 10;
}

.drag-indicator.show {
	opacity: 1;
	transform: translateY(0);
}

/* 选中状态指示器 */
.selection-indicator {
	position: absolute;
	bottom: 10px;
	left: 50%;
	transform: translateX(-50%);
	background-color: rgba(9, 105, 218, 0.9);
	color: white;
	padding: 6px 12px;
	border-radius: 16px;
	font-size: 12px;
	font-weight: 500;
	opacity: 0;
	transform: translateX(-50%) translateY(10px);
	transition: all 0.3s ease;
	z-index: 10;
	backdrop-filter: blur(8px);
}

.selection-indicator.show {
	opacity: 1;
	transform: translateX(-50%) translateY(0);
}

/* 操作反馈动画 */
@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
	}
}

.canvas-container.pulse {
	animation: pulse 0.3s ease-in-out;
}

/* 成功反馈动画 */
@keyframes success-flash {
	0% {
		background-color: #ffffff;
	}
	50% {
		background-color: #dcfce7;
	}
	100% {
		background-color: #ffffff;
	}
}

.canvas-container.success {
	animation: success-flash 0.6s ease-in-out;
}

/* 错误反馈动画 */
@keyframes error-shake {
	0%, 100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-5px);
	}
	75% {
		transform: translateX(5px);
	}
}

.canvas-container.error {
	animation: error-shake 0.5s ease-in-out;
}
</style>
